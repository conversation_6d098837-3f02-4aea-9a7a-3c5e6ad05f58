import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Image,
} from "@heroui/react";
import { Network } from "@/types/network.ts";
import { useSwitchChain } from "wagmi";
import { useState } from "react";

export type NetworkSwitcherProps = {
  isOpen: boolean;
  onOpenChange: () => void;
  fromNetwork: Network;
  toNetwork: Network;
  onConfirm?: () => void;
};

export function NetworkSwitcher(props: NetworkSwitcherProps) {
  const { switchChain, isPending } = useSwitchChain();
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    try {
      setIsLoading(true);
      await switchChain({ chainId: props.toNetwork.chainId });
      props.onConfirm?.();
      props.onOpenChange();
    } catch (error) {
      console.error("Network switch failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    props.onOpenChange();
  };

  return (
    <Modal
      className="max-w-sm"
      hideCloseButton={isLoading}
      isDismissable={!isLoading}
      isOpen={props.isOpen}
      onOpenChange={props.onOpenChange}
      size="sm"
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            scale: 1,
            transition: {
              duration: 0.4,
              ease: "easeOut",
            },
          },
          exit: {
            y: -20,
            opacity: 0,
            scale: 0.95,
            transition: {
              duration: 0.2,
              ease: "easeIn",
            },
          },
        },
      }}
    >
      <ModalContent className="mx-3">
        {() => (
          <>
            <ModalHeader className="text-lg font-semibold text-color8 text-center pb-1 pt-4">
              Switch Network
            </ModalHeader>
            <ModalBody className="px-5 py-3">
              <div className="flex flex-col items-center space-y-4">
                <p className="text-color7 text-xs text-center leading-relaxed opacity-90">
                  Switch your wallet to continue
                </p>

                <div className="flex items-center justify-center w-full gap-6">
                  {/* From Network */}
                  <div className="flex flex-col items-center space-y-2 transition-all duration-300">
                    <span className="text-color7 text-xs font-medium">From</span>
                    <div className="bg-color3 rounded-full p-2 border border-color9 shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105">
                      <Image
                        alt={props.fromNetwork.name}
                        className="size-7 transition-transform duration-300"
                        src={props.fromNetwork.logoUrl}
                      />
                    </div>
                    <span className="text-color8 text-xs font-medium max-w-16 text-center truncate">
                      {props.fromNetwork.name}
                    </span>
                  </div>

                  {/* Arrow */}
                  <div className="flex flex-col items-center">
                    <div className="relative">
                      {/* 背景光晕效果 */}
                      <div className="absolute inset-0 bg-purple1/20 rounded-full animate-ping"></div>
                      <div className="relative bg-gradient-to-r from-purple1 to-purple2 rounded-full p-3 shadow-lg">
                        {/* 使用SVG箭头替代图片，更大更生动 */}
                        <svg
                          className="size-5 text-white animate-bounce"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* To Network */}
                  <div className="flex flex-col items-center space-y-2 transition-all duration-300">
                    <span className="text-purple1 text-xs font-medium">To</span>
                    <div className="relative">
                      {/* 目标网络光环效果 */}
                      <div className="absolute -inset-1 bg-gradient-to-r from-purple1 to-purple2 rounded-full opacity-30 animate-pulse"></div>
                      <div className="relative bg-gradient-to-br from-purple1/10 to-purple1/20 rounded-full p-2 border-2 border-purple1 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
                        <Image
                          alt={props.toNetwork.name}
                          className="size-7 transition-transform duration-300"
                          src={props.toNetwork.logoUrl}
                        />
                      </div>
                    </div>
                    <span className="text-color8 text-xs font-medium max-w-16 text-center truncate">
                      {props.toNetwork.name}
                    </span>
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter className="flex justify-center gap-3 px-5 pb-5 pt-2">
              <Button
                className="bg-color3 text-color8 rounded-full px-6 h-9 min-w-[90px] text-sm font-medium border border-color9 hover:bg-color9/20 transition-all duration-300 hover:scale-105"
                isDisabled={isLoading}
                variant="flat"
                onPress={handleCancel}
              >
                Cancel
              </Button>
              <Button
                className="bg-gradient-to-r from-purple1 to-purple2 text-white rounded-full px-6 h-9 min-w-[90px] text-sm font-medium shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                isDisabled={isLoading || isPending}
                isLoading={isLoading || isPending}
                onPress={handleConfirm}
              >
                {isLoading || isPending ? "Switching..." : "Confirm"}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}

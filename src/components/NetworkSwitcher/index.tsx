import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Ta<PERSON>,
} from "@heroui/react";
import React from "react";
import Token from "@/types/token.ts";

export type TokenSelectorProps = {
  isOpen: boolean;
  onOpenChange: () => void;
};

export function NetworkSwitcher(props: TokenSelectorProps) {
  return (
    <Modal
      className="min-w-[32rem]"
      isOpen={props.isOpen}
      onOpenChange={props.onOpenChange}
    >
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="text-xl font-medium text-color8">
              Select Token
            </ModalHeader>
            <ModalBody>
              <Tabs
                classNames={{
                  tabList:
                    "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                  cursor: "w-full bg-purple1",
                  tab: "max-w-fit px-0 h-12",
                  tabContent:
                    "group-data-[selected=true]:text-purple1 text-color7 text-md",
                }}
                size="md"
                variant="underlined"
              >
                <Tab key="popular" title="Popular Tokens">
                  <TabPanel
                    tokens={
                      popularTokens.length > 0 ? popularTokens : props.tokens
                    }
                    onTokenSelect={handleTokenSelect}
                  />
                </Tab>
                <Tab key="depin" title="Depin Tokens">
                  <TabPanel
                    tokens={depinTokens.length > 0 ? depinTokens : props.tokens}
                    onTokenSelect={handleTokenSelect}
                  />
                </Tab>
                <Tab key="all" title="All Tokens">
                  <TabPanel
                    tokens={props.tokens}
                    onTokenSelect={handleTokenSelect}
                  />
                </Tab>
              </Tabs>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Image,
} from "@heroui/react";
import { Network } from "@/types/network.ts";
import { useSwitchChain } from "wagmi";
import { useState } from "react";

export type NetworkSwitcherProps = {
  isOpen: boolean;
  onOpenChange: () => void;
  fromNetwork: Network;
  toNetwork: Network;
  onConfirm?: () => void;
};

export function NetworkSwitcher(props: NetworkSwitcherProps) {
  const { switchChain, isPending } = useSwitchChain();
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    try {
      setIsLoading(true);
      await switchChain({ chainId: props.toNetwork.chainId });
      props.onConfirm?.();
      props.onOpenChange();
    } catch (error) {
      console.error("Network switch failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    props.onOpenChange();
  };

  return (
    <Modal
      className="max-w-md"
      hideCloseButton={isLoading}
      isDismissable={!isLoading}
      isOpen={props.isOpen}
      onOpenChange={props.onOpenChange}
      size="sm"
    >
      <ModalContent className="mx-4">
        {() => (
          <>
            <ModalHeader className="text-lg font-semibold text-color8 text-center pb-2">
              Switch Network
            </ModalHeader>
            <ModalBody className="px-6 py-4">
              <div className="flex flex-col items-center space-y-4">
                <p className="text-color7 text-sm text-center leading-relaxed">
                  Switch your wallet to continue with the transaction
                </p>

                <div className="flex items-center justify-between w-full max-w-xs">
                  {/* From Network */}
                  <div className="flex flex-col items-center space-y-2">
                    <span className="text-color7 text-xs font-medium">From</span>
                    <div className="bg-color3 rounded-full p-2.5 border border-color9 shadow-sm">
                      <Image
                        alt={props.fromNetwork.name}
                        className="size-7"
                        src={props.fromNetwork.logoUrl}
                      />
                    </div>
                    <span className="text-color8 text-xs font-medium max-w-16 text-center truncate">
                      {props.fromNetwork.name}
                    </span>
                  </div>

                  {/* Arrow */}
                  <div className="flex items-center px-3">
                    <Image
                      className="size-5 opacity-60"
                      src="/images/icon_arrow.svg"
                      alt="switch"
                    />
                  </div>

                  {/* To Network */}
                  <div className="flex flex-col items-center space-y-2">
                    <span className="text-color7 text-xs font-medium">To</span>
                    <div className="bg-color3 rounded-full p-2.5 border-2 border-purple1 shadow-sm">
                      <Image
                        alt={props.toNetwork.name}
                        className="size-7"
                        src={props.toNetwork.logoUrl}
                      />
                    </div>
                    <span className="text-color8 text-xs font-medium max-w-16 text-center truncate">
                      {props.toNetwork.name}
                    </span>
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter className="flex justify-center gap-3 px-6 pb-6 pt-2">
              <Button
                className="bg-color3 text-color8 rounded-full px-6 h-10 min-w-[100px] text-sm font-medium"
                isDisabled={isLoading}
                variant="flat"
                onPress={handleCancel}
              >
                Cancel
              </Button>
              <Button
                className="bg-purple1 text-white rounded-full px-6 h-10 min-w-[100px] text-sm font-medium shadow-lg"
                isDisabled={isLoading || isPending}
                isLoading={isLoading || isPending}
                onPress={handleConfirm}
              >
                {isLoading || isPending ? "Switching..." : "Confirm"}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Image,
} from "@heroui/react";
import { Network } from "@/types/network.ts";
import { useSwitchChain } from "wagmi";
import { useState } from "react";

export type NetworkSwitcherProps = {
  isOpen: boolean;
  onOpenChange: () => void;
  fromNetwork: Network;
  toNetwork: Network;
  onConfirm?: () => void;
};

export function NetworkSwitcher(props: NetworkSwitcherProps) {
  const { switchChain, isPending } = useSwitchChain();
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    try {
      setIsLoading(true);
      await switchChain({ chainId: props.toNetwork.chainId });
      props.onConfirm?.();
      props.onOpenChange();
    } catch (error) {
      console.error("Network switch failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    props.onOpenChange();
  };

  return (
    <Modal
      className="max-w-md"
      hideCloseButton={isLoading}
      isDismissable={!isLoading}
      isOpen={props.isOpen}
      onOpenChange={props.onOpenChange}
      size="sm"
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: "easeOut",
            },
          },
          exit: {
            y: -20,
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: "easeIn",
            },
          },
        },
      }}
    >
      <ModalContent className="mx-4 backdrop-blur-sm">
        {() => (
          <>
            <ModalHeader className="text-lg font-semibold text-color8 text-center pb-2">
              Switch Network
            </ModalHeader>
            <ModalBody className="px-6 py-5">
              <div className="flex flex-col items-center space-y-6">
                <p className="text-color7 text-sm text-center leading-relaxed opacity-80">
                  Switch your wallet to continue with the transaction
                </p>

                <div className="flex items-center justify-between w-full max-w-sm px-2">
                  {/* From Network */}
                  <div className="flex flex-col items-center space-y-3 transition-all duration-300">
                    <span className="text-color7 text-xs font-medium uppercase tracking-wide">From</span>
                    <div className="bg-color3 rounded-full p-3 border border-color9 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
                      <Image
                        alt={props.fromNetwork.name}
                        className="size-8 transition-transform duration-300"
                        src={props.fromNetwork.logoUrl}
                      />
                    </div>
                    <span className="text-color8 text-xs font-medium max-w-20 text-center truncate">
                      {props.fromNetwork.name}
                    </span>
                  </div>

                  {/* Arrow */}
                  <div className="flex items-center px-4">
                    <div className="bg-gradient-to-r from-purple1/10 to-purple1/20 rounded-full p-2 animate-pulse">
                      <Image
                        className="size-6 opacity-70 transition-all duration-500 hover:opacity-100 hover:scale-110"
                        src="/images/icon_arrow.svg"
                        alt="switch"
                      />
                    </div>
                  </div>

                  {/* To Network */}
                  <div className="flex flex-col items-center space-y-3 transition-all duration-300">
                    <span className="text-purple1 text-xs font-medium uppercase tracking-wide">To</span>
                    <div className="bg-gradient-to-br from-purple1/5 to-purple1/10 rounded-full p-3 border-2 border-purple1 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 animate-pulse">
                      <Image
                        alt={props.toNetwork.name}
                        className="size-8 transition-transform duration-300"
                        src={props.toNetwork.logoUrl}
                      />
                    </div>
                    <span className="text-color8 text-xs font-medium max-w-20 text-center truncate">
                      {props.toNetwork.name}
                    </span>
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter className="flex justify-center gap-4 px-6 pb-6 pt-4">
              <Button
                className="bg-color3 text-color8 rounded-full px-8 h-11 min-w-[110px] text-sm font-medium border border-color9 hover:bg-color9/20 transition-all duration-300 hover:scale-105"
                isDisabled={isLoading}
                variant="flat"
                onPress={handleCancel}
              >
                Cancel
              </Button>
              <Button
                className="bg-gradient-to-r from-purple1 to-purple2 text-white rounded-full px-8 h-11 min-w-[110px] text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                isDisabled={isLoading || isPending}
                isLoading={isLoading || isPending}
                onPress={handleConfirm}
              >
                {isLoading || isPending ? "Switching..." : "Confirm"}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}

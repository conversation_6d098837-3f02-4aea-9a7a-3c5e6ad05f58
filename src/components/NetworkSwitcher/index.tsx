import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Image,
} from "@heroui/react";
import { Network } from "@/types/network.ts";
import { useSwitchChain } from "wagmi";
import { useState } from "react";

export type NetworkSwitcherProps = {
  isOpen: boolean;
  onOpenChange: () => void;
  fromNetwork: Network;
  toNetwork: Network;
  onConfirm?: () => void;
};

export function NetworkSwitcher(props: NetworkSwitcherProps) {
  const { switchChain, isPending } = useSwitchChain();
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    try {
      setIsLoading(true);
      await switchChain({ chainId: props.toNetwork.chainId });
      props.onConfirm?.();
      props.onOpenChange();
    } catch (error) {
      console.error("Network switch failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    props.onOpenChange();
  };

  return (
    <Modal
      className="min-w-[32rem]"
      hideCloseButton={isLoading}
      isDismissable={!isLoading}
      isOpen={props.isOpen}
      onOpenChange={props.onOpenChange}
    >
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="text-xl font-medium text-color8">
              Switch Network
            </ModalHeader>
            <ModalBody className="py-6">
              <div className="flex flex-col items-center space-y-6">
                <div className="text-center">
                  <p className="text-color7 text-sm mb-4">
                    You need to switch your wallet network to continue
                  </p>
                </div>

                <div className="flex items-center justify-center space-x-8 w-full">
                  {/* From Network */}
                  <div className="flex flex-col items-center space-y-2">
                    <div className="text-color7 text-sm">From</div>
                    <div className="bg-color3 rounded-full p-3 border border-color9">
                      <Image
                        className="size-8"
                        src={props.fromNetwork.logoUrl}
                        alt={props.fromNetwork.name}
                      />
                    </div>
                    <div className="text-color8 text-sm font-medium">
                      {props.fromNetwork.name}
                    </div>
                  </div>

                  {/* Arrow */}
                  <div className="flex items-center">
                    <Image
                      className="size-6"
                      src="/images/icon_arrow.svg"
                      alt="switch"
                    />
                  </div>

                  {/* To Network */}
                  <div className="flex flex-col items-center space-y-2">
                    <div className="text-color7 text-sm">To</div>
                    <div className="bg-color3 rounded-full p-3 border border-purple1">
                      <Image
                        alt={props.toNetwork.name}
                        className="size-8"
                        src={props.toNetwork.logoUrl}
                      />
                    </div>
                    <div className="text-color8 text-sm font-medium">
                      {props.toNetwork.name}
                    </div>
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter className="flex justify-center space-x-4 pb-6">
              <Button
                className="bg-color3 text-color8 rounded-full px-8 h-12 min-w-[120px]"
                isDisabled={isLoading}
                variant="flat"
                onPress={handleCancel}
              >
                Cancel
              </Button>
              <Button
                className="bg-purple1 text-white rounded-full px-8 h-12 min-w-[120px]"
                isDisabled={isLoading || isPending}
                isLoading={isLoading || isPending}
                onPress={handleConfirm}
              >
                {isLoading || isPending ? "Switching..." : "Confirm"}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}

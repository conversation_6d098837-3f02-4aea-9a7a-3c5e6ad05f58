# NetworkSwitcher Component

A compact and elegant modal component for switching wallet networks with an optimized user interface.

## ✨ Features

- **Ultra-Compact Design**: Optimized modal size (max-w-sm) for minimal footprint
- **Vivid Arrow Animation**: Custom SVG arrow with ping + bounce effects
- **Glowing Network Icons**: Pulsing light halos around target network
- **Tight Spacing**: Perfectly condensed layout with optimal visual hierarchy
- **Smooth Interactions**: Enhanced loading states and micro-interactions
- **Consistent Theming**: Follows project's design system with purple accent colors
- **Automatic Switching**: Seamless wallet network switching using wagmi's `useSwitchChain`

## 🎨 Design Improvements

### Ultra-Compact Layout
- **Modal Size**: Reduced to `max-w-sm` for minimal screen footprint
- **Button Size**: Compact `h-9` with `min-w-[90px]` for tight spacing
- **Icon Size**: Consistent `size-7` for balanced proportions
- **Spacing**: Condensed with `space-y-4` and `gap-6` for efficiency

### Vivid Visual Effects
- **Animated Arrow**: Custom SVG with dual animation (ping + bounce)
- **Glowing Halos**: Pulsing light rings around target network
- **Gradient Backgrounds**: Rich purple gradients for depth
- **Shadow Layers**: Refined shadow system for subtle depth

### Advanced Animation System
- **Modal Entrance**: Scale + slide with 0.4s smooth transition
- **Arrow Animation**: Combined `animate-ping` + `animate-bounce` effects
- **Network Glow**: Pulsing gradient halos with `animate-pulse`
- **Micro-interactions**: Hover scale effects on all interactive elements

## Props

```typescript
export type NetworkSwitcherProps = {
  isOpen: boolean;                    // Controls modal visibility
  onOpenChange: () => void;           // Called when modal should close
  fromNetwork: Network;               // Current network
  toNetwork: Network;                 // Target network to switch to
  onConfirm?: () => void;            // Optional callback after successful switch
};
```

## Usage Example

```tsx
import { NetworkSwitcher } from "@/components/NetworkSwitcher";
import { useDisclosure } from "@heroui/react";
import { networks } from "@/config/network";

function MyComponent() {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  
  const fromNetwork = networks[0]; // Current network (e.g., Ethereum)
  const toNetwork = networks[1];   // Target network (e.g., IoTeX)

  const handleConfirm = () => {
    console.log("Network switched successfully!");
    // Additional logic after network switch
  };

  return (
    <>
      <button onClick={onOpen}>
        Switch Network
      </button>
      
      <NetworkSwitcher
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        fromNetwork={fromNetwork}
        toNetwork={toNetwork}
        onConfirm={handleConfirm}
      />
    </>
  );
}
```

## 🎨 Styling Details

The component uses the project's design system with optimized proportions:

### Colors
- **Primary Text**: `color8` (main content)
- **Secondary Text**: `color7` (labels and descriptions)
- **Backgrounds**: `color3` (cards and containers)
- **Primary Actions**: `purple1` (confirm button and target network border)
- **Borders**: `color9` (subtle borders), `purple1` (emphasis)

### Typography
- **Header**: `text-lg font-semibold` (reduced from text-xl)
- **Labels**: `text-xs font-medium` (compact and clean)
- **Description**: `text-sm leading-relaxed` (improved readability)

### Layout & Spacing
- **Modal**: `max-w-md` with `mx-4` margins for mobile responsiveness
- **Content**: `px-6 py-4` for balanced internal spacing
- **Network Icons**: `size-7` in `p-2.5` containers with `shadow-sm`
- **Buttons**: `h-10` with `px-6` and `min-w-[100px]` for consistency

### Interactive States
- **Loading**: Disabled interactions with loading spinner
- **Hover**: Subtle shadow effects on interactive elements
- **Focus**: Proper focus management for accessibility

## ✨ Animation Features

### Enhanced Entrance Animations
```tsx
motionProps={{
  variants: {
    enter: { y: 0, opacity: 1, scale: 1, transition: { duration: 0.4, ease: "easeOut" } },
    exit: { y: -20, opacity: 0, scale: 0.95, transition: { duration: 0.2, ease: "easeIn" } }
  }
}}
```

### Vivid Arrow Animations
```tsx
{/* 双重动画效果 */}
<div className="absolute inset-0 bg-purple1/20 rounded-full animate-ping"></div>
<svg className="size-5 text-white animate-bounce">
  {/* SVG箭头路径 */}
</svg>
```

### Network Glow Effects
- **Target Network**: Pulsing gradient halo with `animate-pulse`
- **Light Ring**: Absolute positioned glowing border
- **Hover Enhancement**: Scale + shadow elevation on interaction

### Animation Combinations
- `animate-ping` + `animate-bounce` - Dynamic arrow movement
- `animate-pulse` + gradient backgrounds - Glowing network effect
- `hover:scale-105` + shadow transitions - Interactive feedback

## Integration with wagmi

The component automatically handles:

- Network switching via `useSwitchChain` hook
- Loading states during the switch process
- Error handling for failed network switches
- Modal dismissal prevention during loading

## Error Handling

- Failed network switches are logged to console
- Loading states prevent user interaction during switch
- Modal cannot be dismissed while switching is in progress

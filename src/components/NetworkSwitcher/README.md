# NetworkSwitcher Component

A compact and elegant modal component for switching wallet networks with an optimized user interface.

## ✨ Features

- **Compact Design**: Optimized modal size (max-w-md) for better user experience
- **Visual Network Flow**: Clear display of fromNetwork → toNetwork transition
- **Elegant Icons**: Smaller, refined network icons with subtle shadows
- **Responsive Layout**: Perfectly balanced spacing and typography
- **Smooth Interactions**: Loading states and disabled states during switching
- **Consistent Theming**: Follows project's design system (color8, color7, purple1, etc.)
- **Automatic Switching**: Seamless wallet network switching using wag<PERSON>'s `useSwitchChain`

## 🎨 Design Improvements

### Layout & Sizing
- **Modal Size**: Optimized to `max-w-md` with enhanced motion animations
- **Button Size**: Refined to `h-11` with better proportions and hover effects
- **Icon Size**: Balanced at `size-8` for optimal visibility
- **Spacing**: Improved with `space-y-6` for better visual hierarchy

### Visual Enhancements
- **Gradients**: Added subtle gradients for depth and modern appeal
- **Shadows**: Multi-layered shadow system (`shadow-md`, `shadow-lg`, `shadow-xl`)
- **Border Emphasis**: Target network features `border-2 border-purple1`
- **Typography**: Enhanced with `uppercase tracking-wide` for labels

### Animation System
- **Modal Entrance**: Smooth slide-in with opacity transition (0.3s)
- **Hover Effects**: Scale transforms (`hover:scale-105`) on interactive elements
- **Pulse Animation**: Subtle pulse on arrow and target network
- **Transition Timing**: Consistent `duration-300` for smooth interactions

## Props

```typescript
export type NetworkSwitcherProps = {
  isOpen: boolean;                    // Controls modal visibility
  onOpenChange: () => void;           // Called when modal should close
  fromNetwork: Network;               // Current network
  toNetwork: Network;                 // Target network to switch to
  onConfirm?: () => void;            // Optional callback after successful switch
};
```

## Usage Example

```tsx
import { NetworkSwitcher } from "@/components/NetworkSwitcher";
import { useDisclosure } from "@heroui/react";
import { networks } from "@/config/network";

function MyComponent() {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  
  const fromNetwork = networks[0]; // Current network (e.g., Ethereum)
  const toNetwork = networks[1];   // Target network (e.g., IoTeX)

  const handleConfirm = () => {
    console.log("Network switched successfully!");
    // Additional logic after network switch
  };

  return (
    <>
      <button onClick={onOpen}>
        Switch Network
      </button>
      
      <NetworkSwitcher
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        fromNetwork={fromNetwork}
        toNetwork={toNetwork}
        onConfirm={handleConfirm}
      />
    </>
  );
}
```

## 🎨 Styling Details

The component uses the project's design system with optimized proportions:

### Colors
- **Primary Text**: `color8` (main content)
- **Secondary Text**: `color7` (labels and descriptions)
- **Backgrounds**: `color3` (cards and containers)
- **Primary Actions**: `purple1` (confirm button and target network border)
- **Borders**: `color9` (subtle borders), `purple1` (emphasis)

### Typography
- **Header**: `text-lg font-semibold` (reduced from text-xl)
- **Labels**: `text-xs font-medium` (compact and clean)
- **Description**: `text-sm leading-relaxed` (improved readability)

### Layout & Spacing
- **Modal**: `max-w-md` with `mx-4` margins for mobile responsiveness
- **Content**: `px-6 py-4` for balanced internal spacing
- **Network Icons**: `size-7` in `p-2.5` containers with `shadow-sm`
- **Buttons**: `h-10` with `px-6` and `min-w-[100px]` for consistency

### Interactive States
- **Loading**: Disabled interactions with loading spinner
- **Hover**: Subtle shadow effects on interactive elements
- **Focus**: Proper focus management for accessibility

## ✨ Animation Features

### Entrance Animations
```tsx
motionProps={{
  variants: {
    enter: { y: 0, opacity: 1, transition: { duration: 0.3, ease: "easeOut" } },
    exit: { y: -20, opacity: 0, transition: { duration: 0.2, ease: "easeIn" } }
  }
}}
```

### Interactive Animations
- **Network Icons**: Hover scale effect with shadow enhancement
- **Arrow**: Pulsing background with gradient and scale on hover
- **Buttons**: Scale transform with shadow elevation on hover
- **Target Network**: Continuous pulse animation to draw attention

### Transition Classes
- `transition-all duration-300` - Smooth state changes
- `hover:scale-105` - Subtle scale on hover
- `animate-pulse` - Attention-drawing pulse effect
- `hover:shadow-xl` - Dynamic shadow elevation

## Integration with wagmi

The component automatically handles:

- Network switching via `useSwitchChain` hook
- Loading states during the switch process
- Error handling for failed network switches
- Modal dismissal prevention during loading

## Error Handling

- Failed network switches are logged to console
- Loading states prevent user interaction during switch
- Modal cannot be dismissed while switching is in progress

# NetworkSwitcher Component

A modal component for switching wallet networks with a user-friendly interface.

## Features

- Shows current network (fromNetwork) and target network (toNetwork)
- Visual network switching flow with icons and arrows
- Cancel and Confirm buttons
- Loading states during network switching
- Consistent styling with the project theme
- Automatic wallet network switching using wa<PERSON><PERSON>'s `useSwitch<PERSON>hain`

## Props

```typescript
export type NetworkSwitcherProps = {
  isOpen: boolean;                    // Controls modal visibility
  onOpenChange: () => void;           // Called when modal should close
  fromNetwork: Network;               // Current network
  toNetwork: Network;                 // Target network to switch to
  onConfirm?: () => void;            // Optional callback after successful switch
};
```

## Usage Example

```tsx
import { NetworkSwitcher } from "@/components/NetworkSwitcher";
import { useDisclosure } from "@heroui/react";
import { networks } from "@/config/network";

function MyComponent() {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  
  const fromNetwork = networks[0]; // Current network (e.g., Ethereum)
  const toNetwork = networks[1];   // Target network (e.g., IoTeX)

  const handleConfirm = () => {
    console.log("Network switched successfully!");
    // Additional logic after network switch
  };

  return (
    <>
      <button onClick={onOpen}>
        Switch Network
      </button>
      
      <NetworkSwitcher
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        fromNetwork={fromNetwork}
        toNetwork={toNetwork}
        onConfirm={handleConfirm}
      />
    </>
  );
}
```

## Styling

The component uses the project's design system:

- **Colors**: `color8` (text), `color7` (secondary text), `color3` (backgrounds), `purple1` (primary actions)
- **Buttons**: Rounded full style with consistent padding and heights
- **Layout**: Centered content with proper spacing
- **Loading states**: Disabled interactions during network switching

## Integration with wagmi

The component automatically handles:

- Network switching via `useSwitchChain` hook
- Loading states during the switch process
- Error handling for failed network switches
- Modal dismissal prevention during loading

## Error Handling

- Failed network switches are logged to console
- Loading states prevent user interaction during switch
- Modal cannot be dismissed while switching is in progress

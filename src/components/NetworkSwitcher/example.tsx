import React from "react";
import { Button, useDisclosure } from "@heroui/react";
import { NetworkSwitcher } from "./index";
import { networks } from "@/config/network";
import { useChainId } from "wagmi";

/**
 * 使用示例：展示如何在Bridge组件中集成NetworkSwitcher
 */
export function NetworkSwitcherExample() {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const currentChainId = useChainId();
  
  // 示例：当前网络是以太坊，目标网络是IoTeX
  const fromNetwork = networks.find(n => n.chainId === currentChainId) || networks[0];
  const toNetwork = networks.find(n => n.chainId === 4689) || networks[1]; // IoTeX

  const handleConfirm = () => {
    console.log("Network switched successfully!");
    // 这里可以添加网络切换后的逻辑
    // 比如刷新余额、重新获取数据等
  };

  return (
    <div className="p-6">
      <h2 className="text-xl font-medium text-color8 mb-4">
        NetworkSwitcher 使用示例
      </h2>
      
      <div className="space-y-4">
        <div className="text-color7">
          <p>当前网络: {fromNetwork.name}</p>
          <p>目标网络: {toNetwork.name}</p>
        </div>
        
        <Button
          className="bg-purple1 text-white rounded-full px-6 h-12"
          onPress={onOpen}
        >
          切换到 {toNetwork.name}
        </Button>
      </div>

      <NetworkSwitcher
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        fromNetwork={fromNetwork}
        toNetwork={toNetwork}
        onConfirm={handleConfirm}
      />
    </div>
  );
}

/**
 * 在Bridge组件中的集成示例
 */
export function BridgeIntegrationExample() {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const currentChainId = useChainId();
  
  // 假设这些来自Bridge的状态
  const fromNetwork = networks.find(n => n.chainId === currentChainId) || networks[0];
  const requiredNetwork = networks.find(n => n.chainId === 4689) || networks[1];
  
  // 检查是否需要切换网络
  const needsNetworkSwitch = currentChainId !== requiredNetwork.chainId;

  const handleNetworkSwitch = () => {
    if (needsNetworkSwitch) {
      onOpen();
    }
  };

  const handleSwitchConfirm = () => {
    // 网络切换成功后的逻辑
    console.log("Network switched, refreshing data...");
    // 刷新余额、重新获取Token列表等
  };

  return (
    <div className="p-6">
      <h2 className="text-xl font-medium text-color8 mb-4">
        Bridge集成示例
      </h2>
      
      {needsNetworkSwitch ? (
        <div className="bg-color3 rounded-lg p-4 mb-4">
          <p className="text-color7 mb-2">
            请切换到 {requiredNetwork.name} 网络以继续操作
          </p>
          <Button
            className="bg-purple1 text-white rounded-full px-6 h-10"
            onPress={handleNetworkSwitch}
          >
            切换网络
          </Button>
        </div>
      ) : (
        <div className="bg-green-100 rounded-lg p-4 mb-4">
          <p className="text-green-800">
            ✓ 已连接到正确的网络: {fromNetwork.name}
          </p>
        </div>
      )}

      <NetworkSwitcher
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        fromNetwork={fromNetwork}
        toNetwork={requiredNetwork}
        onConfirm={handleSwitchConfirm}
      />
    </div>
  );
}

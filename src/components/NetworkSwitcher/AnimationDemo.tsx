import { Button, useDisclosure } from "@heroui/react";
import { NetworkSwitcher } from "./index";
import { networks } from "@/config/network";

/**
 * 动画演示组件 - 展示NetworkSwitcher的各种动画效果
 */
export function NetworkSwitcherAnimationDemo() {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  
  // 示例网络数据
  const fromNetwork = networks[0] || {
    chainId: 1,
    name: "Ethereum",
    logoUrl: "/images/networks/ethereum.svg"
  };
  
  const toNetwork = networks[1] || {
    chainId: 4689,
    name: "IoTeX",
    logoUrl: "/images/networks/iotex.svg"
  };

  const handleConfirm = () => {
    console.log("Network switch animation completed!");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-6">
      <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full">
        <div className="text-center space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              NetworkSwitcher Demo
            </h1>
            <p className="text-gray-600 text-sm">
              体验优化后的网络切换弹窗动画效果
            </p>
          </div>

          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-700 mb-2">动画特性</h3>
              <ul className="text-sm text-gray-600 space-y-1 text-left">
                <li>• 🎭 Modal入场动画 (slide-in + fade)</li>
                <li>• ✨ 网络图标悬停缩放效果</li>
                <li>• 🔄 箭头脉冲动画</li>
                <li>• 🎯 目标网络持续脉冲</li>
                <li>• 🎨 渐变背景和阴影</li>
                <li>• 🖱️ 按钮悬停动画</li>
              </ul>
            </div>

            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="font-semibold text-blue-700 mb-2">当前配置</h3>
              <div className="text-sm text-blue-600 space-y-1">
                <p><strong>From:</strong> {fromNetwork.name}</p>
                <p><strong>To:</strong> {toNetwork.name}</p>
                <p><strong>动画时长:</strong> 300ms</p>
                <p><strong>缓动函数:</strong> easeOut</p>
              </div>
            </div>
          </div>

          <Button
            className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full px-8 h-12 font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            onPress={onOpen}
          >
            🚀 体验动画效果
          </Button>

          <div className="text-xs text-gray-500">
            点击按钮查看完整的动画体验
          </div>
        </div>
      </div>

      <NetworkSwitcher
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        fromNetwork={fromNetwork}
        toNetwork={toNetwork}
        onConfirm={handleConfirm}
      />
    </div>
  );
}

/**
 * 动画效果说明组件
 */
export function AnimationGuide() {
  return (
    <div className="bg-white rounded-lg p-6 shadow-md">
      <h2 className="text-lg font-semibold mb-4">🎬 动画效果指南</h2>
      
      <div className="space-y-4">
        <div className="border-l-4 border-purple-500 pl-4">
          <h3 className="font-medium text-purple-700">Modal 入场动画</h3>
          <p className="text-sm text-gray-600">
            从上方滑入 + 透明度渐变，营造流畅的视觉体验
          </p>
          <code className="text-xs bg-gray-100 px-2 py-1 rounded">
            y: -20 → 0, opacity: 0 → 1
          </code>
        </div>

        <div className="border-l-4 border-blue-500 pl-4">
          <h3 className="font-medium text-blue-700">悬停交互</h3>
          <p className="text-sm text-gray-600">
            网络图标和按钮支持悬停缩放，提供即时反馈
          </p>
          <code className="text-xs bg-gray-100 px-2 py-1 rounded">
            hover:scale-105 + shadow elevation
          </code>
        </div>

        <div className="border-l-4 border-green-500 pl-4">
          <h3 className="font-medium text-green-700">脉冲动画</h3>
          <p className="text-sm text-gray-600">
            箭头和目标网络使用脉冲动画吸引注意力
          </p>
          <code className="text-xs bg-gray-100 px-2 py-1 rounded">
            animate-pulse + gradient background
          </code>
        </div>

        <div className="border-l-4 border-orange-500 pl-4">
          <h3 className="font-medium text-orange-700">渐变效果</h3>
          <p className="text-sm text-gray-600">
            使用CSS渐变创建现代化的视觉层次
          </p>
          <code className="text-xs bg-gray-100 px-2 py-1 rounded">
            bg-gradient-to-r from-purple1 to-purple2
          </code>
        </div>
      </div>
    </div>
  );
}
